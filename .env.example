# DeSi Configuration File
# Copy this file to .env and modify the values as needed

# Database Configuration
DESI_DB_PATH=desi_vectordb
DESI_COLLECTION_NAME=desi_docs
DESI_MEMORY_DB_PATH=desi_conversation_memory.db

# Model Configuration
DESI_MODEL_NAME=gpt-oss:20b
DESI_EMBEDDING_MODEL_NAME=nomic-embed-text

# Data Configuration
DESI_DATA_DIR=data
DESI_PROCESSED_DATA_DIR=data/processed

# Scraper Configuration
DESI_OPENBIS_URL=https://openbis.readthedocs.io/en/20.10.0-11/index.html
DESI_WIKIJS_URL=https://datastore.bam.de/en/home
# Set to a number to limit pages per scraper, or leave empty for unlimited
DESI_MAX_PAGES_PER_SCRAPER=

# Processing Configuration
DESI_MIN_CHUNK_SIZE=100
DESI_MAX_CHUNK_SIZE=1000
DESI_CHUNK_OVERLAP=50

# Query Configuration
DESI_RETRIEVAL_TOP_K=5
DESI_HISTORY_LIMIT=20

# Web Configuration
DESI_WEB_HOST=127.0.0.1
DESI_WEB_PORT=5000
DESI_WEB_DEBUG=false
DESI_SECRET_KEY=desi_secret_key_change_in_production
DESI_CORS_ORIGINS=*

# Logging Configuration
DESI_LOG_LEVEL=INFO
