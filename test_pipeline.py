#!/usr/bin/env python3
"""
Simple test script to verify the DeSi pipeline works.
"""

import sys
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Test that all imports work."""
    print("Testing imports...")
    
    try:
        from desi.utils.config import DesiConfig
        print("✅ Config import successful")
        
        config = DesiConfig()
        print(f"✅ Config loaded: db_path={config.db_path}")
        
        from desi.processor.unified_processor import UnifiedProcessor
        print("✅ Processor import successful")
        
        from desi.query.conversation_engine import DesiConversationEngine
        print("✅ Conversation engine import successful")
        
        from desi.web.app import create_app
        print("✅ Web app import successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_processor():
    """Test the processor with existing data."""
    print("\nTesting processor...")
    
    try:
        from desi.utils.config import DesiConfig
        from desi.processor.unified_processor import UnifiedProcessor
        
        config = DesiConfig()
        
        # Check if we have data
        project_root = Path(__file__).parent
        input_dir = project_root / config.data_dir / "raw"
        
        if not input_dir.exists():
            print("❌ No raw data directory found")
            return False
            
        # Count files
        openbis_files = list((input_dir / "openbis").glob("*.txt")) if (input_dir / "openbis").exists() else []
        wikijs_files = list((input_dir / "wikijs").glob("*.txt")) if (input_dir / "wikijs").exists() else []
        
        print(f"📁 Found {len(openbis_files)} OpenBIS files, {len(wikijs_files)} Wiki.js files")
        
        if len(openbis_files) == 0 and len(wikijs_files) == 0:
            print("❌ No data files found to process")
            return False
        
        # Test processor initialization
        output_dir = project_root / config.processed_data_dir
        chroma_dir = project_root / config.db_path
        
        processor = UnifiedProcessor(
            input_dir=str(input_dir),
            output_dir=str(output_dir),
            min_chunk_size=config.min_chunk_size,
            max_chunk_size=config.max_chunk_size,
            chunk_overlap=config.chunk_overlap,
            generate_embeddings=False,  # Skip embeddings for quick test
            chroma_dir=str(chroma_dir),
            collection_name=config.collection_name
        )
        
        print("✅ Processor initialized successfully")
        
        # Test processing a single file
        test_files = openbis_files[:1] if openbis_files else wikijs_files[:1]
        if test_files:
            test_file = test_files[0]
            print(f"🧪 Testing with file: {test_file.name}")
            
            chunks = processor.process_file(test_file)
            print(f"✅ Processed {len(chunks)} chunks from test file")
            
            if chunks:
                print(f"📝 Sample chunk preview: {chunks[0]['content'][:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Processor test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Starting DeSi pipeline tests...\n")
    
    success = True
    
    # Test imports
    if not test_imports():
        success = False
    
    # Test processor
    if not test_processor():
        success = False
    
    print(f"\n{'✅ All tests passed!' if success else '❌ Some tests failed!'}")
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
