Title: How to use Masterdata checker
URL: https://datastore.bam.de/en/masterdata_definition/masterdata_checker
Source: datastore
---

/
masterdata_definition
/
masterdata_checker
How to use Masterdata checker
Last edited by
<PERSON><PERSON><PERSON><PERSON>, Caroline
09/04/2025
¶
1. Prepare Masterdata Excel file
Generate the Excel file (.xlsx) of the Object Type or Controlled Vocabulary according to the
definition of Masterdara
and
best practices
.
¶
2. Open the Masterdata checker and upload the Masterdata Excel file
The Masterdata checker can be accessed
here
. Click on the
Datei auswählen
and select the Masterdata Excel file, click on the
Check Masterdata
tab to activate the file checker.  Info, warnings, and errors will be listed below under
Checker Logs
¶
3. Interpretation of the info, warning, and error messages
ERRORs must be
corrected by DSSts before the Masterdata Excel file is shared with the Data Store team via GitHub.
Info
and
warning
messages provide an additional information to users. and do not required any action of the DSSts.
ERROR
messages indicate mistakes in the Masterdata Excel file, which will prevent functioning of the system,
mandatory
to fix before proceeding further. In the error drop-down there will be information displayed about the objective of the mistake, e.g.
Code
is incorect,
Property
is misslabeled etc. In some cases, the specific cell number will be indicated to where mistake is to be found.
Correct the error in the Masterdata Excel file and re-upload the file to check if the problem has been fixed.
INFO
provides general information without requiring any action on the part of the user. It informs the user about the data model used to execute the check; the type of the file uploaded, etc. The last drop-down list shows the name of the files that has just been checked.
WARNING
provides information about the possible issues or  changes in the system but no action is required. Information about the planned future changes in
Property types
can be found in the drop-down list of warnings.
In case you encounter any bugs or would like to give feedback on using the Masterdata Checker, please send a message to the developers at
GitHub
.