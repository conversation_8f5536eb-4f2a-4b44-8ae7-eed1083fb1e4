name: Installing and Testing
on: [push]

# https://docs.github.com/en/actions/using-jobs/assigning-permissions-to-jobs
# `contents` is for permission to the contents of the repository.
# `pull-requests` is for permission to pull request
permissions:
  contents: write
  checks: write
  pull-requests: write

env:
  UV_SYSTEM_PYTHON: true

jobs:
  install-and-test:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python_version: ["3.9", "3.10", "3.11", "3.12"]
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python ${{matrix.python_version}}
      uses: actions/setup-python@v5
      with:
        python-version: ${{matrix.python_version}}
    - name: Install uv
      run: |
        curl -LsSf https://astral.sh/uv/install.sh | sh
    - name: Install dependencies
      run: |
        pip install --upgrade pip
        uv pip install -e '.[dev]'
    # - name: mypy
    #   run: |
    #     python -m mypy --ignore-missing-imports --follow-imports=silent --no-strict-optional bam_masterdata tests
    - name: Test with pytest
      run: |
        python -m pytest -sv --ignore=tests/cli --ignore=tests/metadata/test_entities_dict.py tests
  build-and-install:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.12
      uses: actions/setup-python@v5
      with:
        python-version: 3.12
    - name: Install uv
      run: |
        curl -LsSf https://astral.sh/uv/install.sh | sh
    - name: Build the package
      run: |
        uv pip install build
        python -m build --sdist
    - name: Install the package
      run: |
        uv pip install dist/*.tar.gz
  ruff-linting:
      runs-on: ubuntu-latest
      steps:
        - uses: actions/checkout@v4
        - uses: chartboost/ruff-action@v1
          with:
            args: "check ."
  ruff-formatting:
      runs-on: ubuntu-latest
      steps:
        - uses: actions/checkout@v4
        - uses: chartboost/ruff-action@v1
          with:
            args: "format . --check --verbose"