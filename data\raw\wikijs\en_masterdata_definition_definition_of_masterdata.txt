Title: The Process of Masterdata Definition
URL: https://datastore.bam.de/en/masterdata_definition/definition_of_masterdata
Source: datastore
---

/
masterdata_definition
/
definition_of_masterdata
The Process of Masterdata Definition
Page Contents
The Process of Masterdata Definition
Masterdata Excel Template examples
Naming Masterdata Excel Files
Color-Coding of Masterdata Excel Files
Organising Masterdata Definition Files
Last edited by
<PERSON><PERSON>, Angela
09/05/2025
¶
The Process of Masterdata Definition
Only instance admins can register
Masterdata
in openBIS. Since Data Store Stewards (DSSt) have group admin but not instance admin rights in the
main
Data Store instance, they cannot register domain/division-specific Masterdata on their own.
They can use one of the Masterdata Excel templates. To create an Object Type e.g.,
Instrument
or a Controlled Vocabulary e.g.,
DFG_DEVICE_CODE
.
Please ensure that you follow the
rules and best practices for Masterdata definition
and check the Excel files with the
Masterdata checker
before you make them available to the Data Store team via GitHub repository or via email at
<EMAIL>
. Information about how to create a new repository in the Github can be found
here
.  You can also create your repository or fork it from
GitHub BAM research
.  To Share your GitHub repository with the Data Store team, go to your repository in GitHub, copy the URL from the browser address bar and sent it via email to
<EMAIL>
.
The instance admins of the Data Store team check the Masterdata defined by the divisions and contact the DSSt in case of questions. As soon as the Masterdata for the division is finalised, the instance admins import it into the
main
Data Store instance.
If you want to propose changes to an existing Object Type or Controlled Vocabulary. You can use one of following options:
Create an "Issue" in the repository
bam-masterdata repository
. Current Object Types and Controlled Vocabularies can be seen within this folder under
data model
.
Download the Excel format of the
Object Type
or
Controlled Vocabulary
you want to modify from the current openBIS Masterdata. Modify it accoring to
Color-Coding of Masterdata Excel files
and
Naming Masterdata Excel Files
.
¶
Masterdata Excel Template examples
We provide examples of the Masterdata Excel file to create an Object Type i.e.,
Instrument
and a controlled vocabulary i.e.,
DFG_DEVICE_CODE
.
The openBIS Masterdata Excel template uses terms from earlier openBIS versions (which should not be changed):
"
Object
type" is called "SAMPLE_TYPE"
"
Collection
type" is called "EXPERIMENT_TYPE"
If you use a German Excel version,  note that the terms TRUE/FALSE (shown in the columns "Mandatory" and "Show in edit views") are automatically renamed to WAHR/FALSCH and must be changed to English.
¶
Naming Masterdata Excel Files
Please create a separate Excel file for each entity type/controlled vocabulary.
The name of each Masterdata Excel file should include:
the entity type:
object_type
or
vocabulary
the code of the entity type/vocabulary should be included while maintaining the exact format (upper-case letters, inlcuding all underscores and dots).
the number of the division
The different parts of the file name have to be separated by underscores.
¶
Example:
For the
Object
type INSTRUMENT, the Masterdata Excel file should be named
object_type_INSTRUMENT_FBX.Y.xlsx
.
¶
Color-Coding of Masterdata Excel Files
A new entity type typically contains a combination of pre-existing
Property
types and newly defined
Property
types.
In order to easily distinguish between them in the Masterdata Excel file, they should be color-coded to facilitate the Masterdata review process by the Data Store team. This is particularily important when defining an entity type that is a specification of an existing, more generic entity type, and thus "inherits" the set of
Property
types from the generic one, as described
here
.
Property
type assignments should be color-coded according to the following rules:
New
Property
types are marked in
orange
.
Existing
Property
types that are already assigned to the more generic entity type are marked in
blue
.
Existing
Property
types that are not assigned to the more generic entity type are marked in
green
.
¶
Example:
A user wants to create a new
Object
type INSTRUMENT.CAMERA that is a specification of the existing generic
Object
type INSTRUMENT. INSTRUMENT.CAMERA inherits the complete set of
Property
types from INSTRUMENT, color-coded in
blue
(e.g., MANUFACTURER). The user also defines several new
Property
types to describe attributes that are specific to a camera; these are color-coded in
orange
(e.g., IMAGE_SENSOR_FRAMERATE). In addition, the user assigns an existing
Property
type (e.g., FIRMWARE_VERSION) that has already been assigned to other
Object
types but not to INSTRUMENT. This should be color-coded in
green
.
¶
Organising Masterdata Definition Files
Each entity type (except
Property
types)/controlled vocabulary must be represented in a separate Excel file for easier organisation and findability.
Please make sure that the Excel file also includes all (newly created) dependencies such as new controlled vocabularies, new Dynamic Property and Entity Validation Scripts. Ensure that the individual Excel files and the vocabulary code listed in the Excel file of the object type are consistent.
If the Masterdata you generate uses existing vocabulary types (e.g., BAM_OE) or scripts (e.g. date_range_validation.py), they don't need to be sent.