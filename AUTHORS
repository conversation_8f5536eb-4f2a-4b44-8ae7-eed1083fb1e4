# This is the list of BAM Masterdata's significant contributors.
#
# This does not necessarily list everyone who has contributed code,
# especially since many employees of one corporation may be contributing to the
# definition of object types, property types, and vocabularies in the Masterdata
# datamodel.
# To see the full list of code contributors, see the revision history in
# source control.

- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON><PERSON><PERSON>