Title: Best Practices for Masterdata Definition
URL: https://datastore.bam.de/en/masterdata_definition/best_practices
Source: datastore
---

/
masterdata_definition
/
best_practices
Best Practices for Masterdata Definition
Page Contents
Best Practices for Masterdata Definition
Naming Spaces and Projects
Defining Entity Types
"Inheritance" of Entity Types
Making Changes to existing Entity Types
Last edited by
<PERSON><PERSON>, Angela
09/04/2025
¶
Best Practices for Masterdata Definition
To ensure a consistent set of
Masterdata
in the BAM Data Store, we are introducing some rules, naming conventions, and recommendations when creating
Spaces
,
Projects
, entity types (
Collection
,
Object
,
Dataset
, and
Property
types) and controlled vocabularies.
In general, Masterdata should be
as generic as possible and as specific as necessary
. It is encouraged to re-use existing entity types and vocabularies from other groups. For this reason, the use of division-specific information in the codes and labels of entity types (e.g., "BAM_FBX.Y_ROOM_TEMPERATURE") should be avoided.
¶
Naming
Spaces
and
Projects
Spaces
and
Projects
have a
code/PermID
and a
description
, but no additional label or metadata properties. Therefore, the
Space
/
Project
code and description should contain enough information to make it clear what it is for (both to people outside the group and to people who join the group later).
Once created, the code of a
Space
/
Project
cannot
be changed.
¶
Space
/
Project
Code:
Can only contain A-Z (uppercase letters), 0-9, '_' (underscore), '-' (hyphen), and '.' (dot).
Words that would normally be separated by a whitespace, should instead be separated by an underscore ("_").
Should be meaningful.
Should be in English.
Should be between 3-30 characters.
Despite being written in capital letters only, the code of a
Space
/
Project
will be displayed in the main menu with uppercase letters for every first letter of a new word (as signified by the use of an underscore) followed by lowercase letters.
¶
Example:
A
Space
or
Project
with the code "TEST.THING_UNDERSCORE-HYPHEN" will be displayed as "Test.thing  Underscore-hyphen" in the main menu.
¶
Space
/
Project
Description:
Not mandatory in openBIS but mandatory for the Data Store.
Should contain enough detail to be understandable to people outside of the group.
Should contain 2-50 words.
Should be in English followed by a double slash (//) and a German translation in the following format: "English description//Deutsche Beschreibung"
¶
Defining Entity Types
When defining a new entity type of class
Collection
,
Object
or
Dataset
, please check whether a similar entity type (of the same class) already exists that could be reused.
If the new entity type is a specification of an existing, more generic entity type then the new, more specific entity type must include all the Sections and
Property
types of the existing entity type. For more information on the concept of inheritance of entity types, see
here
.
Collection and Dataset types will be deprecated in future openBIS versions. Therefore only Object Types and Controlled Vocabularies are curently defined for the Data Store.
¶
Defining
Object
Types
The creation of new
Object
types is one of the main tasks of the
Masterdata definition process
.
¶
Object
Code:
Only capital letters allowed.
Can only contain A-Z, 0-9, _ and . (dot).
Words should be separated by an underscore ("_").
Should be meaningful.
Should be in English.
Should be between 3-20 characters long.
¶
Object
Description:
Not mandatory in openBIS but mandatory for BAM Data Store.
Should contain enough details to be understandable to people outside of the group.
Should be up to 250 characters long.
Should be in English followed by a German translation in the following format: "English description//Deutsche Beschreibung"
¶
Object
Generated Code Prefix:
Should be meaningful.
As a convention, we recommend to use the
first 3 letters of the
Object
type code
.
Example:
For the
Object
type "Instrument", the code prefix should be "INS". Registered
Objects
of the type "INSTRUMENT" will have the code "INS1", "INS2", etc.
If an identical code prefix already exists:
Choose the first 4-5 letters of the
Object
type code OR
If the
Object
type is a specification of a an already existing
Object
type (see above), take the existing generated code prefix, add a dot/period (.) and then another 2-4 character code to make it unique.
Example:
For a new
Object
type "EXPERIMENTAL_STEP.MICROSCOPY" that is a specification of the existing
Object
type "EXPERIMENTAL_STEP" with the code prefix "EXP", the code prefix of the new
Object
type could be "EXP.MIC".
¶
Defining
Property
Types
A
Property
is a metadata field used to describe an entity, i.e.
Collection
, an
Object
or a
Dataset
.
Property
types have a
code
, a
label
, a
data type
, and a
description
. One and the same
Property
type can be used for many entity types. When assigning a
Property
type to an entity type, it can further be defined whether the Property is
mandatory
and whether it should be
editable
by the user in the ELN-LIMS UI. Additionally, a
Dynamic Property Script
can be added to the
Propert
y type assignment.
All
Property
types are
global
in openBIS which means that they can be assigned to many entity types at the same time. Changes made to a
Property
type (e.g., changing the label or the description) will thus affect all entity types that the
Property
type is assigned to and should therefore be considered carefully. The only non-global attributes of a
Property
type are "Mandatory" and "Editable" and the
Dynamic Property Script
which have to be defined individually for each
Property
type assignment. More information on changing existing entity types can be found
here.
¶
Internal
Property
Types:
There are several internal
Property
types in the Data Store (pre-defined by openBIS). Their code begins with a "$" sign. Some of them should be reused when creating a new entity Type (e.g., $NAME, $XMLCOMMENTS), others are used internally only (e.g., $ELN_SETTINGS). No changes can be made to these internal
Property
types which is why they don't necessarily follow the naming conventions defined below.
A new
Object
type should always contain the predefined
Property
types $NAME, $XMLCOMMENTS and $ANNOTATIONS_STATE. The latter is not visible in any of the forms, but it is necessary to establish parent-child relationships between
Objects
.
¶
Property
Code:
Only capital letters allowed.
Can only contain A-Z, 0-9 and _, -,.
Words should be separated by an underscore ("_").
Should be meaningful.
Should be in English.
Should be between 3-20 characters long.
¶
Property
Label:
Should be meaningful.
Should be in English.
¶
Property
Data Type:
A Property type can use one of 11 possible data types:
Data type
Description
BOOLEAN
yes or no (checkbox)
INTEGER
integer number
REAL
decimal number
DATE
date field
TIMESTAMP
date with timestamp
VARCHAR
one-line text
MULTILINE_VARCHAR
long text (it is possible to enable a Rich Text Editor for this type of property)
HYPERLINK
URL
CONTROLLED_VOCABULARY
list of predefined (alpha-numeric) values
XML
to be used by Managed Properties and for Spreadsheet components
OBJECT
1-1 connection to a specific Object type
¶
Property Description:
Not mandatory in openBIS but mandatory for the Data Store.
Should contain enough details to be understandable to people outside of the group.
Should be up to 250 characters long.
Should be in English followed by a German translation in the following format: "English description//Deutsche Beschreibung"
¶
Representation of Values and Units of Measurement of Physical Quantities:
Units of measurement (e.g., SI base units) of a physical quantity should be represented as part of the label/description of a
Property
type and not as an individual
Property
type. The unit should be specified in square brackets in the
Property
label. Where relevant, use the English notation to indicate the decimal place of numbers (period instead of comma). Additionally, the unit can also be part of the code to avoid multiple similarly named
Property
types representing different entities.
Example:
To specify the room temperature measured in °C, create a single
Property
type called ROOM_TEMP_IN_CELCIUS with the data type REAL, instead of creating two types ROOM_TEMP_VALUE (data type REAL) and ROOM_TEMP_UNIT (data type CONTROLLED_VOCABULARY).
Code
Mandatory
Show in edit views
Section
Property label
Data type
Description
Metadata
Dynamic script
ROOM_TEMP_IN_CELSIUS
False
TRUE
Further information
Room temperature [°C]
REAL
Room temperature in °C//Raumtemperatur in °C
¶
Assignment of existing
Property
Types
It is encouraged to reuse/assign already existing
Property
types for the definition of new entity types instead of creating new
Property
types that are synonymous or similar in meaning to those already in use.
Example:
Instead of creating a new
Property
type called INSTRUMENT_NAME for an
Object
type INSTRUMENT, it is recommended to assign the existing
Property
type $NAME.
¶
Defining Controlled Vocabularies
A controlled vocabulary is an established list of terms to ensure consistency and uniqueness in the description of a given domain, e.g., a list of room labels, SI units or purity grades. In openBIS controlled vocabularies are one possible data type for metadata
Properties
. The vocabulary itself has a
code
and a
description
and each term in the vocabulary has a
code
, a
label
, and a
description
.
When defining a new controlled vocabulary, please check whether a similar vocabulary already exists.
All existing controlled vocabularies and their terms are listed in the Vocabulary Browser under the Utilities main menu of the ELN-LIMS UI.
Your vocabulary should contain at least three different terms. If your controlled vocabulary consists only of two terms, consider using Boolean values (TRUE/FALSE) instead.
It is not possible to choose several terms from the same vocabulary in one metadata
Property
.
¶
Vocabulary Code:
Only capital letters allowed.
Can only contain A-Z, 0-9 and _, -,.
Words should be separated by an underscore ("_").
Should be meaningful.
Should be in English.
Should be between 3-20 characters long.
¶
Vocabulary Description:
Not mandatory in openBIS but mandatory for the Data Store.
Should contain enough details to be understandable to people outside of the group.
Should be up to 250 characters long.
Should be in English followed by a German translation in the following format: "English description//Deutsche Beschreibung"
¶
Vocabulary URL Template:
Some controlled vocabularies are documented in the web and have unique and persistent identifiers for each term (e.g., a persistent URL or a DOI). In openBIS, it is possible to define a URL template for this type of vocabulary, which represents the vocabulary-specific part of the URL followed by
${term}
. The term-specific part of the URL must be identical to the
term code
defined in openBIS.
If vocabulary terms with a URL/DOI are used in the ELN-LIMS UI, these are displayed as hyperlinks.
Example:
For a controlled vocabulary "UNITS_OF_MEASURE" which includes terms from the IUPAC Gold Book, the URL template is
https://doi.org/10.1351/goldbook.${term}
. The DOI for the term with the label "degree Celsius" is
https://doi.org/10.1351/goldbook.D01561
. Accordingly, the openBIS term code for "degree Celsius" must be "D01561".
¶
Term Code:
Only capital letters allowed.
Can only contain A-Z, 0-9 and _, -,.
Words should be separated by an underscore ("_").
Should be meaningful.
Should be in English.
Should be between 3-20 characters long.
When using a prefix at the beginning of the code, it recommended to use the same prefix for the codes of all terms of a vocabulary.
Example:
In the vocabulary "DFG_DEVICE_CODE" vocabulary (for DFG Gerätegruppenschlüssel), the prefix could be "DFG_" for all terms, e.g., "DFG_0000", "DFG_0005", "DFG_0010" etc.
Should not contain only numbers, unless the number is part of an external URL/DOI for the term (see below).
Example:
In the vocabulary “PURITY_GRADE”, for a term with the label “2.0” and the description “gas purity level 2.0 (99.0%)//Gasreinheit 2.0 (99,0 %), the code should not be “2.0” but something more meaningful like “GAS_PUR_2.0”.
If the vocabulary makes use of a
URL template
to link to definitions of the terms, the term code has to be identical with the term-specific part of the URL/DOI. In this case, it may be necessary to include codes that consist only of numbers.
¶
Term Label:
Should be meaningful.
Should be in English.
Can be up to 128 characters long.
¶
Term Description:
Not mandatory in openBIS but mandatory for the Data Store.
Should contain enough details to be understandable to people outside of the group.
Should be up to 250 characters long.
Should be in English followed by a German translation in the following format: "English description//Deutsche Beschreibung"
¶
"Inheritance" of Entity Types
When defining a new entity type that is similar to an existing one but requires further specification (i.e., additional
Property
types), we make use of the principle of
inheritance
. This concept is borrowed from object-oriented programming:
"Most object-oriented programming languages have another feature that differentiates them from other data abstraction languages; class inheritance. Each class has a superclass from which it inherits operations and internal structure. A class can add to the operations it inherits or can redefine inherited operations. However, classes cannot delete inherited operations." -
Designing reusable classes. RE Johnson, B Foote. Journal of object-oriented programming. 1988.
In case of the BAM Data Store, entity types (
Collection
,
Object
and
Dataset
types) inherit attributes from an entity type of the same class (
Collection
types inherit from
Collection
types,
Object
types inherit from
Object
types etc.):
The new, more specific entity type includes all the Sections,
Property
types, and
Dynamic Property and Entity Validation Scripts
of the existing, more generic entity type.
The code of the new entity type contains the name of the original entity type as a prefix followed by a dot/period (.) followed by the specification term as a suffix: GENERIC.SPECIFIC
Likewise, the generated code prefix of the new entity type contains the code prefix of the original entity type followed by a dot/period (.) followed by the new code prefix: GEN.SPE
The attributes of the
Property
type assignments ("Mandatory", "Editable") of the existing entity type may be changed but only from FALSE to TRUE (and not the other way round from TRUE to FALSE).
The inheritance concept can also be applied across multiple levels of entity types (e.g., GENERIC.SPECIFIC_LEVEL_1.SPECIFIC_LEVEL_2). The same rules apply.
The principle of inheritance is not native to openBIS but has been developed for the Data Store which is why the above described rules have to be implemented
manually
when defining new entity types.
Example:
The existing Object type INSTRUMENT (generated code prefix: INS) does not contain the
Property
types needed to adequately describe the metadata of a camera. Therefore, a new
Object
type with the name INSTRUMENT.CAMERA (generated code prefix: INS.CAM) is created.
The more specific
Object
type INSTRUMENT.CAMERA inherits all original Sections ("General information", "BAM information", "Details", "Comments") and
Property
types (e.g., "$NAME", "DEVICE_MODEL_NAME", "BAM_OE" etc.) from the more generic
Object
type INSTRUMENT. The attributes ("Mandatory", "Editable") of the the
Property
type assignments can be adjusted from FALSE to TRUE (e.g., DEVICE_MODEL_NAME was set as non-mandatory in INSTRUMENT but is defined as mandatory in INSRUMENT.CAMERA). Dynamic Property Scripts stay the same.
In addition, the
Object
type INSTRUMENT.CAMERA contains two new Sections ("Camera Information", "Software information") with additional
Property
types (e.g., "IMAGE_SENSOR_FRAMERATE, "LENS_MOUNT_TYPE", "FIRMWARE_VERSION") that are
not
included in the
Object
type INSTRUMENT.
¶
Making Changes to existing Entity Types
It is possible to edit entity types after they have been initially created. These changes affect all entities of the type in question that have already been created in the Data Store. For this reason, changes to existing entity types in the Data Store always have to be discussed with the Data Store team and, if applicable, the person who originally created the entity type to ensure metadata consistency.
If you want to suggest changes to existing Masterdata please follow the same procedure described for
The Process of Masterdata Definition
.
¶
Editing an existing
Property
Type:
This should only be done in case of minor corrections (e.g., correction of typos, additions or clarifications) in the label or the description of the
Property
type. The meaning of
Property
types should never be changed retrospectively when
Properties
of the type are already assigned to entity types that are in use in the Data Store instance.
The code of a
Property
type cannot be changed after its creation nor can a
Dynamic Property Script
be assigned retrospectively.
¶
Assignment of a new
Property
Type to an existing Entity Type:
The assignment of a
non-mandatory
Property
type
(either new or existing) is possible but has to be discussed with the Data Store team and, if applicable, the person who originally created the entity type.
The assignment of a
mandatory
Property
type
(either new or existing) is only possible if a default value is defined for all entities to which the
Property
type is assigned. The necessity of assigning a mandatory
Property
type and the default value have to be discussed with the Data Store team and, if applicable, the person who originally created the entity type.
¶
Deletion of a
Property
Type Assignment from an existing Entity Type:
This is possible only in exceptional cases because removing the assignment will also remove all existing
Property
values of this type in the Data Store instance -
data will be lost
! The necessity of the deletion has to be discussed with the Data Store team and, if applicable, the person who originally created the entity type, as well as all users who already registered entities of the type in question.
¶
Deletion of an existing
Collection
/
Object
/
Dataset
Type:
This is possible only in exceptional cases because removing the entity type will also remove all existing entities of this type in the Data Store instance -
data will be lost
! The necessity of the deletion has to be discussed with the Data Store team and, if applicable, the person who originally created the entity type, as well as all users who already registered entities of the type in question.