Title: Use Barcodes and QR Codes
URL: https://datastore.bam.de/en/How_to_guides/Use_barcodes_qr_codes
Source: datastore
---

/
How_to_guides
/
Use_barcodes_qr_codes
Use Barcodes and QR Codes
Page Contents
🔧 Use Barcodes and QR Codes
✅ Prerequisites
🪪 Step 1: Choose the Code Content
🖨️ Step 2: Generate the Codes
🧾 Step 3: Print Code
📥 Step 4: Print and Apply Stickers
Hardware to print Barcodes/QR codes
📡 Step 5: Scan and Use in openBIS
Alternative Barcode/QR code batch label generator
Last edited by
<PERSON><PERSON>, Angela
09/18/2025
¶
🔧 Use Barcodes and QR Codes
To enable fast and secure referencing of physical objects (e.g., samples, instruments) in openBIS using barcodes or QR codes.
¶
✅ Prerequisites
Access to an openBIS instance
Registered Objects in openBIS - Inventory (e.g., Samples, Instruments)
Barcode/QR code reader (USB or Bluetooth)
Sticker printer or external code generator (optional)
¶
🪪 Step 1: Choose the Code Content
When an Object is registered, a Default Barcode is automatically generated by openBIS.
Default Barcode
is found under
Identification Info
and can be displayed to print under
More
drop-down menu,
Barcode/QR Code Print
.
It is also possible to use the PermId to generate a Barcode/QR code.
¶
Option A:
Default Barcode = PermId
Default Barcode is vissible under Identification Info of a registered Object and displayed under the More drop-down menu within the Object Form.
Pros
:
Always available and unique
Compact (suitable for Micro-QR)
Cons
:
Tied to one openBIS instance
Changes on export/import
Only available after object registration
¶
Option B:
$BARCODE Property
$BARCODE is an internal openBIS Property.
Pros
:
Can be pre-assigned and batch imported
Compatible across openBIS intances
Cons
:
Uniqueness not enforced
Requires additional data management to keep uniqueness and consistency
¶
🖨️ Step 2: Generate the Codes
¶
Option A:
In openBIS ELN
Direct integration
Limited formatting
¶
Option B:
with external Tools
Linux
:
qrencode
(CLI, scriptable)
Windows
:
Zint
(GUI, flexible)
Label Printer Software
:
Often supports Excel import
Good formatting and printer integration
¶
🧾 Step 3: Print Code
¶
Select the Code Format
Choose based on your hardware and space constraints:
Code Type
Format
Pros
Notes
Code 128
1D
Widely supported
Minimum standard
QR Code
2D
Compact, robust
Recommended
Micro-QR Code
2D
Very small (5x5mm)
Ideal for PermIds
💡
Tip
: Always prefer 2D codes unless you have a specific reason to use 1D.
¶
📥 Step 4: Print and Apply Stickers
Use durable stickers compatible with your printer
Include optional metadata (e.g., contact, organizational unit)
Apply to physical objects clearly and accessibly
¶
Hardware to print Barcodes/QR codes
Alternative to workflow and printers suggested in openBIS documentation of the ETHZ
Printers
. FB 9.3 tested following hardware:
Printers:
Option 1: Brother QL-820NWB Label Printer.
Option 2: Brother QL-700, KdB 21464-01=60000544023
Use of P-Touch Editor Software to customize and desing  templates.
Scanner:
Inateck BCST-91 2D Barcode Scanner.
¶
📡 Step 5: Scan and Use in openBIS
Use barcode/QR readers (USB-HID or Bluetooth)
Scanned codes will:
Display object info
Link samples/devices in experiments
¶
Alternative Barcode/QR code batch label generator
A Data Store advanced Label App is being developed by FB 6.5 to simplify the interface between physical Samples generated at the Laboratory and the Data Store
https://github.com/BAMresearch/dalapp