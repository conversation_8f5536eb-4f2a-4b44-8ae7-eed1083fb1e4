Title: Search - Datasets
URL: https://datastore.bam.de/en/How_to_guides/Search_Dataset_Lab_Notebook
Source: datastore
---

/
How_to_guides
/
Search_Dataset_Lab_Notebook
Search - Datasets
Last edited by
<PERSON><PERSON><PERSON><PERSON>, Caroline
09/15/2025
To search through Datasets, navigate to main menu on the left side, open the
Utilities
drop-down menu and select
Advanced Search
. In the
Search For
drop-down menu, select the
Dataset
option and select the
AND
operator to add additional search parameters. For example,  to search for a Dataset by the name of the Registrator, select the option
Property
in the drop-down menu under
Field Type
; the option
Registrator [ATTR.REGISTRATOR]
under
Field Name
, and the option
thatEqualsUserId (UserId)
from the
Comparator Operator
. To define the search value, start typing the Name of the Registrator in the
Field Value
and click on the Search icon, next to the operator field Using (e.g., AND) to activate the search.
Open Utilities
Select Advanced Search
Select Dataset in the Search For drop-down menu
Select AND operator in the Using drop-down menu
Select Property in the drop-down menu under Field Type
Select  Registrator [ATTR.REGISTRATOR] under Field Name
Select thatEqualsUserId (UserId) from the Comparator Operator
Start typing the Name of the Registrator in the Field Value
Click on Search icon.